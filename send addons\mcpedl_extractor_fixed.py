# -*- coding: utf-8 -*-
"""
مستخرج MCPEDL محسن يعمل مع HTML الحقيقي
"""

import re
import json
from bs4 import BeautifulSoup
from typing import Dict, List, Optional, Any
from urllib.parse import urljoin

class MCPEDLExtractorFixed:
    """مستخرج محسن يعمل مع HTML الحقيقي من MCPEDL"""

    def extract_mod_data(self, html_content: str, url: str) -> Optional[Dict[str, Any]]:
        """استخراج بيانات المود من HTML"""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            
            mod_data = {
                'name': self.extract_title(soup),
                'description': self.extract_description(soup),
                'description_arabic': '',  # سيتم إنشاؤه لاحقاً
                'category': self.extract_category(soup),
                'image_urls': self.extract_images(soup, url),
                'version': self.extract_version(soup),
                'size': self.extract_size(soup),
                'download_url': self.extract_download_url(soup, url),
                'creator_name': self.extract_creator_name(soup),
                'creator_contact_info': '',
                'creator_social_channels': [],
                'source_url': url
            }
            
            print(f"استخراج البيانات:")
            print(f"- الاسم: {mod_data['name']}")
            print(f"- الفئة: {mod_data['category']}")
            print(f"- المطور: {mod_data['creator_name']}")
            print(f"- عدد الصور: {len(mod_data['image_urls'])}")
            print(f"- طول الوصف: {len(mod_data['description'])} حرف")
            
            return mod_data
            
        except Exception as e:
            print(f"خطأ في استخراج البيانات: {e}")
            return None

    def extract_title(self, soup: BeautifulSoup) -> str:
        """استخراج عنوان المود"""
        # محاولة استخراج من h1
        h1_element = soup.find('h1', class_='post-page__title')
        if h1_element:
            title = h1_element.get_text().strip()
            return self.clean_title(title)
        
        # محاولة استخراج من title tag
        title_element = soup.find('title')
        if title_element:
            title = title_element.get_text().strip()
            # إزالة "| Minecraft PE Addons" وما شابه
            title = title.split('|')[0].strip()
            return self.clean_title(title)
        
        return ""

    def clean_title(self, title: str) -> str:
        """تنظيف عنوان المود"""
        # إزالة النصوص غير المرغوبة
        unwanted = [
            '| Minecraft PE Addons',
            '| MCPEDL',
            '- MCPEDL',
            'MCPEDL'
        ]
        
        for unwanted_text in unwanted:
            title = title.replace(unwanted_text, '').strip()
        
        # اختصار الأسماء الطويلة
        if ': Community Edition' in title:
            title = title.replace(': Community Edition', '')
        elif ' Community Edition' in title:
            title = title.replace(' Community Edition', '')
        
        return title.strip()

    def extract_description(self, soup: BeautifulSoup) -> str:
        """استخراج وصف المود"""
        description_parts = []
        
        # البحث في changelog
        changelog_div = soup.find('div', class_='changelog-description')
        if changelog_div:
            paragraphs = changelog_div.find_all('p')
            for p in paragraphs:
                text = p.get_text().strip()
                if text and len(text) > 10:
                    # تجاهل العناوين
                    if not text.startswith('Dragon Mounts Community Edition'):
                        description_parts.append(text)
        
        # البحث في entry content
        entry_div = soup.find('div', class_='entry')
        if entry_div:
            paragraphs = entry_div.find_all('p')
            for p in paragraphs[:3]:  # أول 3 فقرات
                text = p.get_text().strip()
                if text and len(text) > 20:
                    description_parts.append(text)
        
        # دمج الوصف
        if description_parts:
            description = ' '.join(description_parts[:3])  # أول 3 أجزاء
            return description[:800]  # حد أقصى
        
        # وصف افتراضي
        return "A Minecraft addon that adds new features and content to enhance your gameplay experience."

    def extract_category(self, soup: BeautifulSoup) -> str:
        """استخراج فئة المود"""
        # البحث في breadcrumbs
        breadcrumbs = soup.find('span', class_='content__title category_list')
        if breadcrumbs:
            links = breadcrumbs.find_all('a')
            for link in links:
                text = link.get_text().lower()
                if 'addon' in text:
                    return 'Addons'
                elif 'shader' in text:
                    return 'Shaders'
                elif 'texture' in text or 'pack' in text:
                    return 'Texture Pack'
        
        # افتراضي
        return 'Addons'

    def extract_images(self, soup: BeautifulSoup, base_url: str) -> List[str]:
        """استخراج روابط الصور"""
        image_urls = []
        
        # البحث في og:image
        og_image = soup.find('meta', {'data-hid': 'og:image'})
        if og_image and og_image.get('content'):
            image_url = og_image['content']
            if self.is_valid_image_url(image_url):
                image_urls.append(image_url)
        
        # البحث في محتوى الصفحة
        content_areas = soup.find_all(['div'], class_=['post-page__content', 'entry', 'changelog-description'])
        
        for area in content_areas:
            images = area.find_all('img')
            for img in images:
                src = img.get('src') or img.get('data-src')
                if src:
                    if src.startswith('//'):
                        src = 'https:' + src
                    elif src.startswith('/'):
                        src = urljoin(base_url, src)
                    
                    if self.is_valid_image_url(src) and src not in image_urls:
                        image_urls.append(src)
        
        return image_urls[:10]  # أول 10 صور

    def is_valid_image_url(self, url: str) -> bool:
        """التحقق من صحة رابط الصورة"""
        if not url or len(url) < 10:
            return False
        
        # تجاهل الصور الثابتة
        static_images = [
            'logo', 'icon', 'avatar', 'favicon', 'empty.png',
            'shield.png', '_nuxt', 'header', 'footer'
        ]
        
        url_lower = url.lower()
        if any(static in url_lower for static in static_images):
            return False
        
        # التحقق من امتداد الصورة
        image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        if not any(ext in url_lower for ext in image_extensions):
            return False
        
        return True

    def extract_version(self, soup: BeautifulSoup) -> str:
        """استخراج إصدار Minecraft المدعوم"""
        # البحث في قسم الإصدارات
        versions_div = soup.find('div', class_='versions')
        if versions_div:
            version_links = versions_div.find_all('a')
            if version_links:
                # أخذ أول إصدار
                first_version = version_links[0].get_text().strip()
                return first_version
        
        return "1.21+"

    def extract_size(self, soup: BeautifulSoup) -> str:
        """استخراج حجم الملف"""
        # البحث في قسم التحميلات
        downloads_div = soup.find('div', id='downloads')
        if downloads_div:
            download_items = downloads_div.find_all('span')
            for item in download_items:
                text = item.get_text()
                # البحث عن حجم الملف
                size_match = re.search(r'\(([^)]+)\)', text)
                if size_match:
                    return size_match.group(1)
        
        return ""

    def extract_download_url(self, soup: BeautifulSoup, base_url: str) -> str:
        """استخراج رابط التحميل"""
        # هذا يحتاج معالجة خاصة لأن MCPEDL يستخدم JavaScript للتحميل
        # نعيد الرابط الأصلي للصفحة
        return base_url

    def extract_creator_name(self, soup: BeautifulSoup) -> str:
        """استخراج اسم المطور"""
        # البحث في معلومات المطور
        creator_div = soup.find('div', class_='creator-name')
        if creator_div:
            creator_link = creator_div.find('a')
            if creator_link:
                return creator_link.get_text().strip()
        
        # البحث في التعليقات
        author_elements = soup.find_all(class_='comments--autor')
        if author_elements:
            return author_elements[0].get_text().strip()
        
        return ""

def test_extractor_with_saved_html():
    """اختبار المستخرج مع HTML المحفوظ"""
    print("🧪 اختبار المستخرج مع HTML المحفوظ...")
    
    try:
        # قراءة HTML المحفوظ
        with open('debug_specific_url.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        extractor = MCPEDLExtractorFixed()
        test_url = "https://mcpedl.com/dragon-mounts-v1-3-25/"
        
        result = extractor.extract_mod_data(html_content, test_url)
        
        if result:
            print("✅ نجح الاستخراج!")
            print(f"النتيجة: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print("❌ فشل الاستخراج")
            return False
            
    except FileNotFoundError:
        print("❌ ملف HTML غير موجود")
        return False
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    test_extractor_with_saved_html()
