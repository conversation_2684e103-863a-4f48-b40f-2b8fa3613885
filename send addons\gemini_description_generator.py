# -*- coding: utf-8 -*-
"""
مولد الوصف بالعربية والإنجليزية باستخدام Gemini
"""

import google.generativeai as genai
from typing import Dict, Optional, Tuple
import json
import re

class GeminiDescriptionGenerator:
    """مولد الوصف باستخدام Gemini AI"""
    
    def __init__(self, api_key: str):
        """تهيئة Gemini"""
        try:
            genai.configure(api_key=api_key)
            self.model = genai.GenerativeModel('gemini-pro')
            print("✅ تم تهيئة Gemini بنجاح")
        except Exception as e:
            print(f"❌ خطأ في تهيئة Gemini: {e}")
            self.model = None
    
    def generate_descriptions(self, mod_data: Dict, extracted_content: str) -> Tuple[str, str]:
        """إنشاء وصف بالإنجليزية والعربية"""
        if not self.model:
            return self.get_fallback_descriptions(mod_data)
        
        try:
            # إنشاء الوصف الإنجليزي
            english_desc = self.generate_english_description(mod_data, extracted_content)
            
            # إنشاء الوصف العربي
            arabic_desc = self.generate_arabic_description(mod_data, extracted_content, english_desc)
            
            return english_desc, arabic_desc
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف: {e}")
            return self.get_fallback_descriptions(mod_data)
    
    def generate_english_description(self, mod_data: Dict, content: str) -> str:
        """إنشاء وصف إنجليزي مفصل"""
        
        prompt = f"""
Create a detailed, engaging English description for this Minecraft addon:

**Mod Name:** {mod_data.get('name', 'Unknown')}
**Category:** {mod_data.get('category', 'Addon')}
**Creator:** {mod_data.get('creator_name', 'Unknown')}
**Version:** {mod_data.get('version', '1.21+')}

**Extracted Content from Page:**
{content[:2000]}

**Instructions:**
1. Write a compelling description (150-300 words)
2. Focus on features, gameplay benefits, and what makes this addon special
3. Use engaging language that appeals to Minecraft players
4. Include key features as bullet points if applicable
5. Mention compatibility and installation if relevant
6. Make it sound exciting and worth downloading

**Format:**
Start with an engaging introduction, then describe main features, and end with a call to action.

**Example style:**
"Transform your Minecraft world with [Addon Name]! This incredible addon brings [main feature] to your gameplay experience. 

Key Features:
• [Feature 1]
• [Feature 2] 
• [Feature 3]

Perfect for players who want to [benefit]. Compatible with Minecraft {mod_data.get('version', '1.21+')} and easy to install.

Download now and revolutionize your Minecraft adventure!"

Generate the description:
"""
        
        try:
            response = self.model.generate_content(prompt)
            description = response.text.strip()
            
            # تنظيف الوصف
            description = self.clean_description(description)
            
            print(f"✅ تم إنشاء الوصف الإنجليزي: {len(description)} حرف")
            return description
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف الإنجليزي: {e}")
            return self.get_fallback_english_description(mod_data)
    
    def generate_arabic_description(self, mod_data: Dict, content: str, english_desc: str) -> str:
        """إنشاء وصف عربي مفصل"""
        
        prompt = f"""
أنشئ وصفاً عربياً مفصلاً وجذاباً لهذا المود في ماين كرافت:

**اسم المود:** {mod_data.get('name', 'غير معروف')}
**الفئة:** {mod_data.get('category', 'إضافة')}
**المطور:** {mod_data.get('creator_name', 'غير معروف')}
**الإصدار:** {mod_data.get('version', '1.21+')}

**الوصف الإنجليزي المرجعي:**
{english_desc}

**المحتوى المستخرج:**
{content[:1000]}

**التعليمات:**
1. اكتب وصفاً جذاباً باللغة العربية (150-300 كلمة)
2. ركز على المميزات وفوائد اللعب وما يجعل هذا المود مميزاً
3. استخدم لغة شيقة تجذب لاعبي ماين كرافت العرب
4. اذكر المميزات الرئيسية كنقاط إذا أمكن
5. اذكر التوافق والتثبيت إذا كان ذلك مناسباً
6. اجعله يبدو مثيراً ويستحق التحميل

**النمط المطلوب:**
ابدأ بمقدمة جذابة، ثم اوصف المميزات الرئيسية، واختتم بدعوة للتحميل.

**مثال على الأسلوب:**
"حوّل عالم ماين كرافت الخاص بك مع [اسم المود]! هذا المود الرائع يجلب [الميزة الرئيسية] إلى تجربة اللعب الخاصة بك.

المميزات الرئيسية:
• [ميزة 1]
• [ميزة 2]
• [ميزة 3]

مثالي للاعبين الذين يريدون [الفائدة]. متوافق مع ماين كرافت {mod_data.get('version', '1.21+')} وسهل التثبيت.

حمّل الآن وأحدث ثورة في مغامرة ماين كرافت الخاصة بك!"

أنشئ الوصف:
"""
        
        try:
            response = self.model.generate_content(prompt)
            description = response.text.strip()
            
            # تنظيف الوصف
            description = self.clean_arabic_description(description)
            
            print(f"✅ تم إنشاء الوصف العربي: {len(description)} حرف")
            return description
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الوصف العربي: {e}")
            return self.get_fallback_arabic_description(mod_data)
    
    def clean_description(self, description: str) -> str:
        """تنظيف الوصف الإنجليزي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'\*\*[^*]+\*\*',  # Bold markdown
            r'Generate the description:',
            r'Description:',
            r'Here\'s the description:',
            r'Here is the description:'
        ]
        
        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)
        
        # تنظيف المسافات
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()
        
        return description
    
    def clean_arabic_description(self, description: str) -> str:
        """تنظيف الوصف العربي"""
        # إزالة النصوص غير المرغوبة
        unwanted_patterns = [
            r'أنشئ الوصف:',
            r'الوصف:',
            r'إليك الوصف:',
            r'هذا هو الوصف:'
        ]
        
        for pattern in unwanted_patterns:
            description = re.sub(pattern, '', description, flags=re.IGNORECASE)
        
        # تنظيف المسافات
        description = re.sub(r'\n\s*\n', '\n\n', description)
        description = description.strip()
        
        return description
    
    def get_fallback_descriptions(self, mod_data: Dict) -> Tuple[str, str]:
        """أوصاف احتياطية في حالة فشل Gemini"""
        english = self.get_fallback_english_description(mod_data)
        arabic = self.get_fallback_arabic_description(mod_data)
        return english, arabic
    
    def get_fallback_english_description(self, mod_data: Dict) -> str:
        """وصف إنجليزي احتياطي"""
        name = mod_data.get('name', 'This Minecraft Addon')
        category = mod_data.get('category', 'addon')
        version = mod_data.get('version', '1.21+')
        
        return f"""Enhance your Minecraft experience with {name}! This amazing {category.lower()} brings exciting new features and content to your world.

Key Features:
• New gameplay mechanics and content
• High-quality textures and models  
• Smooth performance and compatibility
• Easy installation and setup

Perfect for players looking to expand their Minecraft adventure with fresh content. Compatible with Minecraft {version} and designed for optimal performance.

Download now and discover what makes this {category.lower()} special!"""
    
    def get_fallback_arabic_description(self, mod_data: Dict) -> str:
        """وصف عربي احتياطي"""
        name = mod_data.get('name', 'هذا المود')
        category = mod_data.get('category', 'إضافة')
        version = mod_data.get('version', '1.21+')
        
        return f"""عزز تجربة ماين كرافت الخاصة بك مع {name}! هذه {category} الرائعة تجلب مميزات ومحتوى جديد ومثير إلى عالمك.

المميزات الرئيسية:
• آليات لعب ومحتوى جديد
• تكسشرز ونماذج عالية الجودة
• أداء سلس وتوافق ممتاز
• تثبيت وإعداد سهل

مثالية للاعبين الذين يبحثون عن توسيع مغامرة ماين كرافت مع محتوى جديد. متوافقة مع ماين كرافت {version} ومصممة للأداء الأمثل.

حمّل الآن واكتشف ما يجعل هذه {category} مميزة!"""

def test_gemini_generator():
    """اختبار مولد الوصف"""
    print("🧪 اختبار مولد الوصف...")
    
    # بيانات تجريبية
    test_mod_data = {
        'name': 'Dragon Mounts',
        'category': 'Addons',
        'creator_name': 'Tomanex',
        'version': '1.21.81'
    }
    
    test_content = """
    Dragon Mounts Community Edition brings dragons to your Minecraft world.
    Features include Cherry Dragon and Phantom Dragon with unique abilities.
    Compatible with latest Minecraft versions.
    """
    
    # محاولة استخدام مفتاح API (يجب توفيره)
    try:
        # يجب استبدال هذا بمفتاح API حقيقي
        generator = GeminiDescriptionGenerator("your-api-key-here")
        
        if generator.model:
            english, arabic = generator.generate_descriptions(test_mod_data, test_content)
            print(f"✅ الوصف الإنجليزي: {english[:100]}...")
            print(f"✅ الوصف العربي: {arabic[:100]}...")
        else:
            print("⚠️ لم يتم تهيئة Gemini - استخدام الأوصاف الاحتياطية")
            english, arabic = generator.get_fallback_descriptions(test_mod_data)
            print(f"✅ الوصف الاحتياطي الإنجليزي: {english[:100]}...")
            print(f"✅ الوصف الاحتياطي العربي: {arabic[:100]}...")
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    test_gemini_generator()
